import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/ticket_model.dart';
import '../controllers/auth_controller.dart';
import '../services/ticket_service.dart';

class TicketController extends GetxController {
  final TicketService _ticketService = TicketService();
  final RxList<Ticket> tickets = <Ticket>[].obs;
  final RxList<Ticket> upcomingTickets = <Ticket>[].obs;
  final RxList<Ticket> pastTickets = <Ticket>[].obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  late AuthController _authController;

  @override
  void onInit() {
    super.onInit();
    _authController = Get.find<AuthController>();
    loadTickets();

    // Listen to auth changes to reload tickets when user logs in/out
    ever(_authController.isLoggedInObs, (_) {
      loadTickets();
    });
  }

  Future<void> loadTickets() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      tickets.clear();
      upcomingTickets.clear();
      pastTickets.clear();

      if (_authController.isLoggedIn) {
        // User is logged in, load from Firestore using TicketService
        await _loadFromFirestore();
      } else {
        // User is not logged in, load from local storage
        await _loadFromLocalStorage();
      }

      // Categorize tickets
      _categorizeTickets();
    } catch (e) {
      errorMessage.value = 'Failed to load tickets: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _loadFromFirestore() async {
    try {
      print('TicketController: Loading tickets from Firestore...');
      final loadedTickets = await _ticketService.getUserTickets();
      print(
          'TicketController: Loaded ${loadedTickets.length} tickets from Firestore');

      for (var ticket in loadedTickets) {
        print(
            'TicketController: Ticket - ${ticket.movieTitle} (${ticket.id}) - Date: ${ticket.date} - Status: ${ticket.status.name}');
      }

      tickets.value = loadedTickets;
    } catch (e) {
      print('TicketController: Error loading from Firestore: $e');
      // If Firestore fails, try to load from local storage as fallback
      await _loadFromLocalStorage();
    }
  }

  void _categorizeTickets() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    print('TicketController: Categorizing ${tickets.length} tickets...');
    print('TicketController: Today is: $today');

    final upcoming = <Ticket>[];
    final past = <Ticket>[];

    for (final ticket in tickets) {
      try {
        final ticketDate = DateTime.parse(ticket.date);
        print(
            'TicketController: Ticket ${ticket.movieTitle} - Date: ${ticket.date} -> Parsed: $ticketDate');

        if (ticketDate.isAfter(today) || ticketDate.isAtSameMomentAs(today)) {
          upcoming.add(ticket);
          print('TicketController: -> Added to upcoming');
        } else {
          past.add(ticket);
          print('TicketController: -> Added to past');
        }
      } catch (e) {
        print('TicketController: Error parsing date ${ticket.date}: $e');
        // If date parsing fails, consider it as past ticket
        past.add(ticket);
      }
    }

    print(
        'TicketController: Categorization complete - Upcoming: ${upcoming.length}, Past: ${past.length}');

    upcomingTickets.value = upcoming;
    pastTickets.value = past;
  }

  Future<void> _loadFromLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ticketsJson = prefs.getStringList('tickets') ?? [];

      final List<Ticket> loadedTickets = [];
      for (var json in ticketsJson) {
        final Map<String, dynamic> data = jsonDecode(json);

        // Convert string date to Timestamp for local storage
        if (data['purchase_date'] is String) {
          data['purchase_date'] =
              Timestamp.fromDate(DateTime.parse(data['purchase_date']));
        }

        loadedTickets.add(Ticket.fromJson(data));
      }

      // Sort by purchase date (newest first)
      loadedTickets.sort((a, b) => b.purchaseDate.compareTo(a.purchaseDate));
      tickets.value = loadedTickets;
    } catch (e) {
      errorMessage.value = 'Failed to load tickets from local storage: $e';
    }
  }

  Future<bool> purchaseTicket(Ticket ticket) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      if (_authController.isLoggedIn) {
        // Create ticket using TicketService
        final createdTicket = await _ticketService.createTicket(ticket);
        tickets.add(createdTicket);
      } else {
        // Add to local tickets list
        tickets.add(ticket);
      }

      // Save to local storage as backup
      await _saveToLocalStorage();

      // Recategorize tickets
      _categorizeTickets();

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to purchase ticket: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Get upcoming tickets
  List<Ticket> getUpcomingTickets() {
    return upcomingTickets.toList();
  }

  // Get past tickets
  List<Ticket> getPastTickets() {
    return pastTickets.toList();
  }

  // Cancel ticket
  Future<bool> cancelTicket(String ticketId) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      if (_authController.isLoggedIn) {
        await _ticketService.cancelTicket(
            ticketId, 0.0); // Refund amount can be calculated
      }

      // Remove from local list
      tickets.removeWhere((ticket) => ticket.id == ticketId);

      // Save to local storage
      await _saveToLocalStorage();

      // Recategorize tickets
      _categorizeTickets();

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to cancel ticket: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _saveToLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final List<String> ticketsJson = tickets.map((ticket) {
        final Map<String, dynamic> data = ticket.toJson();

        // Convert Timestamp to string for local storage
        if (data['purchase_date'] is Timestamp) {
          data['purchase_date'] =
              (data['purchase_date'] as Timestamp).toDate().toIso8601String();
        }

        return jsonEncode(data);
      }).toList();

      await prefs.setStringList('tickets', ticketsJson);
    } catch (e) {
      errorMessage.value = 'Failed to save tickets to local storage: $e';
    }
  }
}
