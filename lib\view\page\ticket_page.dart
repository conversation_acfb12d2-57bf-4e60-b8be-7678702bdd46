import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../controllers/ticket_controller.dart';
import '../../models/ticket_model.dart';

class TicketPage extends StatelessWidget {
  const TicketPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ticketController = Get.find<TicketController>();

    return SafeArea(
      child: DefaultTabController(
        length: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'my_tickets'.tr,
                style: GoogleFonts.mulish(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 20),
              // Tab bar
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: TabBar(
                  indicator: BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.white70,
                  labelStyle: GoogleFonts.mulish(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                  tabs: const [
                    Tab(text: 'Upcoming'),
                    Tab(text: 'Past'),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: TabBarView(
                  children: [
                    _buildTicketList(ticketController, isUpcoming: true),
                    _buildTicketList(ticketController, isUpcoming: false),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTicketList(TicketController controller,
      {required bool isUpcoming}) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      final tickets =
          isUpcoming ? controller.upcomingTickets : controller.pastTickets;

      if (tickets.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.confirmation_number_outlined,
                size: 80,
                color: Colors.white.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                isUpcoming ? 'No upcoming tickets' : 'No past tickets',
                style: GoogleFonts.mulish(
                  fontSize: 18,
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                isUpcoming
                    ? 'Book your first movie ticket!'
                    : 'Your ticket history will appear here',
                style: GoogleFonts.mulish(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.5),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        itemCount: tickets.length,
        itemBuilder: (context, index) {
          final ticket = tickets[index];
          return _buildTicketCard(ticket, controller, isUpcoming);
        },
      );
    });
  }

  Widget _buildTicketCard(
      Ticket ticket, TicketController controller, bool isUpcoming) {
    final dateFormat = DateFormat('MMM d, yyyy');
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white.withOpacity(0.1),
        border: ticket.status == TicketStatus.cancelled
            ? Border.all(color: Colors.red.withOpacity(0.5), width: 1)
            : null,
      ),
      child: Column(
        children: [
          // Status indicator for cancelled tickets
          if (ticket.status == TicketStatus.cancelled)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.2),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Text(
                'CANCELLED',
                textAlign: TextAlign.center,
                style: GoogleFonts.mulish(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ),
          // Movie info section
          Row(
            children: [
              // Movie poster
              ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: ticket.status == TicketStatus.cancelled
                      ? Radius.zero
                      : const Radius.circular(12),
                  bottomLeft: const Radius.circular(12),
                ),
                child: Image.network(
                  ticket.moviePosterPath != null
                      ? 'https://image.tmdb.org/t/p/w500${ticket.moviePosterPath}'
                      : 'https://via.placeholder.com/500x750?text=No+Image',
                  height: 120,
                  width: 80,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 120,
                      width: 80,
                      color: Colors.grey[800],
                      child: const Center(
                        child: Icon(
                          Icons.image_not_supported,
                          color: Colors.white54,
                        ),
                      ),
                    );
                  },
                ),
              ),
              // Movie details
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        ticket.movieTitle,
                        style: GoogleFonts.mulish(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            size: 14,
                            color: Colors.white70,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            ticket.date,
                            style: GoogleFonts.mulish(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(
                            Icons.access_time,
                            size: 14,
                            color: Colors.white70,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            ticket.time,
                            style: GoogleFonts.mulish(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      // Booking code
                      Row(
                        children: [
                          const Icon(
                            Icons.confirmation_number,
                            size: 14,
                            color: Colors.white70,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            ticket.bookingCode,
                            style: GoogleFonts.mulish(
                              fontSize: 12,
                              color: Colors.white70,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              // Cancel button for upcoming tickets
              if (isUpcoming && ticket.status == TicketStatus.confirmed)
                Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: IconButton(
                    onPressed: () => _showCancelDialog(ticket, controller),
                    icon: const Icon(
                      Icons.cancel_outlined,
                      color: Colors.red,
                      size: 20,
                    ),
                  ),
                ),
            ],
          ),
          // Divider with scissors
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              children: [
                const Icon(
                  Icons.circle,
                  size: 16,
                  color: Color(0xff2B5876),
                ),
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    height: 1,
                    color: Colors.white24,
                  ),
                ),
                const Icon(
                  Icons.circle,
                  size: 16,
                  color: Color(0xff2B5876),
                ),
              ],
            ),
          ),
          // Ticket details section
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildTicketDetail('Theater', ticket.theaterName),
                    _buildTicketDetail('Screen', ticket.screenName),
                    _buildTicketDetail(
                        'Price', currencyFormat.format(ticket.finalPrice)),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildTicketDetail('Seats', _formatSeats(ticket.seats)),
                    _buildTicketDetail('Status', _getStatusText(ticket.status)),
                    _buildTicketDetail(
                        'Payment', _formatPaymentMethod(ticket.paymentMethod)),
                  ],
                ),
              ],
            ),
          ),
          // Purchase date
          Padding(
            padding:
                const EdgeInsets.only(bottom: 12.0, left: 12.0, right: 12.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  'Purchased: ${dateFormat.format(ticket.purchaseDate)}',
                  style: GoogleFonts.mulish(
                    fontSize: 12,
                    color: Colors.white54,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatSeats(List<TicketSeat> seats) {
    if (seats.isEmpty) return 'N/A';
    if (seats.length == 1) return seats.first.seatId;
    return '${seats.first.seatId} +${seats.length - 1}';
  }

  String _getStatusText(TicketStatus status) {
    switch (status) {
      case TicketStatus.confirmed:
        return 'Confirmed';
      case TicketStatus.used:
        return 'Used';
      case TicketStatus.cancelled:
        return 'Cancelled';
      case TicketStatus.expired:
        return 'Expired';
    }
  }

  String _formatPaymentMethod(String method) {
    switch (method) {
      case 'credit_card':
        return 'Credit Card';
      case 'debit_card':
        return 'Debit Card';
      case 'e_wallet':
        return 'E-Wallet';
      case 'cash':
        return 'Cash';
      default:
        return method;
    }
  }

  void _showCancelDialog(Ticket ticket, TicketController controller) {
    Get.dialog(
      AlertDialog(
        backgroundColor: const Color(0xff1a1a1a),
        title: Text(
          'Cancel Ticket',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Are you sure you want to cancel this ticket?\n\nMovie: ${ticket.movieTitle}\nDate: ${ticket.date}\nTime: ${ticket.time}',
          style: GoogleFonts.mulish(
            color: Colors.white70,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'No',
              style: GoogleFonts.mulish(
                color: Colors.white70,
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              final success = await controller.cancelTicket(ticket.id);
              if (success) {
                Get.snackbar(
                  'Success',
                  'Ticket cancelled successfully',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  'Error',
                  controller.errorMessage.value,
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: Text(
              'Yes, Cancel',
              style: GoogleFonts.mulish(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTicketDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.mulish(
            fontSize: 12,
            color: Colors.white54,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.mulish(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ],
    );
  }
}
